"""
Web image testing module for Hong Kong currency recognition.
Tests various configurations with web image URLs.
"""

import os
import sys
import json
import time
import requests
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse
import logging

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

from currency_recognizer import C<PERSON>rencyRecognizer, RecognitionResult
from image_processor import ImageProcessor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WebImageTester:
    """Test currency recognition with web images."""
    
    def __init__(self, api_key: str = None):
        """
        Initialize the web image tester.
        
        Args:
            api_key: Gemini API key
        """
        self.recognizer = CurrencyRecognizer(api_key)
        self.test_results = {}
        self.output_dir = "web_test_results"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Sample web image URLs for testing (using placeholder URLs)
        self.sample_urls = [
            # Add actual test URLs here when available
            # These are examples - replace with real HK currency image URLs
        ]
        
        logger.info("Web image tester initialized")
    
    def validate_url(self, url: str) -> bool:
        """
        Validate if URL is accessible and returns an image.
        
        Args:
            url: Image URL to validate
            
        Returns:
            True if URL is valid and accessible
        """
        try:
            parsed = urlparse(url)
            if not all([parsed.scheme, parsed.netloc]):
                return False
            
            # Check if URL is accessible
            response = requests.head(url, timeout=10)
            if response.status_code != 200:
                return False
            
            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            return content_type.startswith('image/')
            
        except Exception as e:
            logger.warning(f"URL validation failed for {url}: {e}")
            return False
    
    def test_url_with_all_configs(self, image_url: str) -> Dict[str, RecognitionResult]:
        """
        Test a web image URL with all available configurations.
        
        Args:
            image_url: URL of image to test
            
        Returns:
            Dictionary with results for each configuration
        """
        if not self.validate_url(image_url):
            logger.error(f"Invalid or inaccessible URL: {image_url}")
            return {}
        
        logger.info(f"Testing web image: {image_url}")
        
        # Load test configurations for web images
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'api_presets.json')
        
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            test_configs = config_data.get('test_configurations', {}).get('web_image_tests', [])
        except FileNotFoundError:
            logger.warning("Config file not found, using default web tests")
            test_configs = [
                {"name": "web_enhanced", "preset": "web_enhanced", "preprocessing_enabled": True},
                {"name": "balanced_web", "preset": "balanced", "preprocessing_enabled": True},
                {"name": "fast_response", "preset": "fast_response", "preprocessing_enabled": False}
            ]
        
        results = {}
        
        for config in test_configs:
            config_name = config['name']
            preset = config['preset']
            preprocessing = config['preprocessing_enabled']
            
            logger.info(f"Running web test: {config_name}")
            
            try:
                result = self.recognizer.recognize_currency(
                    image_url,
                    preset_name=preset,
                    enable_preprocessing=preprocessing,
                    save_processed_image=True,
                    output_dir=os.path.join(self.output_dir, "processed_images")
                )
                
                results[config_name] = result
                
                # Log result summary
                if result.success:
                    logger.info(f"✓ {config_name}: Success in {result.processing_time:.2f}s")
                else:
                    logger.error(f"✗ {config_name}: Failed - {result.error_message}")
                
                # Longer delay for web requests to respect rate limits
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error in web test {config_name}: {e}")
                results[config_name] = RecognitionResult(
                    success=False,
                    analysis={},
                    processing_time=0,
                    config_used=preset,
                    preprocessing_applied=preprocessing,
                    image_info={},
                    error_message=str(e)
                )
        
        return results
    
    def test_streaming_vs_nonstreaming(self, image_url: str) -> Dict[str, RecognitionResult]:
        """
        Compare streaming vs non-streaming responses for web images.
        
        Args:
            image_url: URL of image to test
            
        Returns:
            Comparison results
        """
        if not self.validate_url(image_url):
            logger.error(f"Invalid URL for streaming test: {image_url}")
            return {}
        
        logger.info(f"Testing streaming vs non-streaming for: {image_url}")
        
        results = {}
        
        # Test with streaming enabled
        logger.info("Testing with streaming enabled")
        results["streaming"] = self.recognizer.recognize_currency(
            image_url,
            preset_name="balanced",  # Balanced preset has streaming enabled
            enable_preprocessing=True
        )
        
        time.sleep(2)
        
        # Test with streaming disabled
        logger.info("Testing with streaming disabled")
        results["non_streaming"] = self.recognizer.recognize_currency(
            image_url,
            preset_name="high_accuracy_local",  # High accuracy has streaming disabled
            enable_preprocessing=True
        )
        
        return results
    
    def test_web_search_grounding(self, image_url: str) -> Dict[str, RecognitionResult]:
        """
        Test with and without web search grounding.
        
        Args:
            image_url: URL of image to test
            
        Returns:
            Comparison results
        """
        if not self.validate_url(image_url):
            logger.error(f"Invalid URL for grounding test: {image_url}")
            return {}
        
        logger.info(f"Testing web search grounding for: {image_url}")
        
        results = {}
        
        # Test without web search grounding
        logger.info("Testing without web search grounding")
        results["no_grounding"] = self.recognizer.recognize_currency(
            image_url,
            preset_name="balanced",
            enable_preprocessing=True
        )
        
        time.sleep(3)
        
        # Test with web search grounding
        logger.info("Testing with web search grounding")
        results["with_grounding"] = self.recognizer.recognize_currency(
            image_url,
            preset_name="web_enhanced",  # Web enhanced has grounding enabled
            enable_preprocessing=True
        )
        
        return results
    
    def batch_test_urls(self, urls: List[str], preset: str = "balanced_web") -> Dict[str, RecognitionResult]:
        """
        Test multiple web image URLs with the same configuration.
        
        Args:
            urls: List of image URLs
            preset: Configuration preset to use
            
        Returns:
            Dictionary with results for each URL
        """
        results = {}
        
        for url in urls:
            if not self.validate_url(url):
                logger.warning(f"Skipping invalid URL: {url}")
                continue
            
            url_name = f"url_{len(results) + 1}"
            logger.info(f"Testing {url_name}: {url}")
            
            result = self.recognizer.recognize_currency(
                url,
                preset_name=preset,
                enable_preprocessing=True
            )
            
            results[url_name] = result
            
            # Respect rate limits with longer delays
            time.sleep(3)
        
        return results
    
    def test_different_image_sources(self, test_urls: List[str] = None) -> Dict[str, Any]:
        """
        Test different types of web image sources.
        
        Args:
            test_urls: List of URLs to test (uses sample URLs if not provided)
            
        Returns:
            Comprehensive test results
        """
        urls_to_test = test_urls or self.sample_urls
        
        if not urls_to_test:
            logger.warning("No URLs provided for testing")
            return {}
        
        logger.info(f"Testing {len(urls_to_test)} web image sources")
        
        comprehensive_results = {
            "test_summary": {
                "total_urls": len(urls_to_test),
                "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "test_type": "web_images"
            },
            "url_results": {},
            "streaming_comparison": {},
            "grounding_comparison": {},
            "configuration_performance": {}
        }
        
        # Test each URL with all configurations
        for i, url in enumerate(urls_to_test):
            url_name = f"url_{i + 1}"
            logger.info(f"Comprehensive web testing: {url_name}")
            
            # All configurations test
            config_results = self.test_url_with_all_configs(url)
            if config_results:
                comprehensive_results["url_results"][url_name] = config_results
            
            # Streaming comparison (use first valid URL only)
            if not comprehensive_results["streaming_comparison"] and config_results:
                streaming_results = self.test_streaming_vs_nonstreaming(url)
                if streaming_results:
                    comprehensive_results["streaming_comparison"][url_name] = streaming_results
            
            # Grounding comparison (use second valid URL if available)
            if len(comprehensive_results["url_results"]) == 2 and not comprehensive_results["grounding_comparison"]:
                grounding_results = self.test_web_search_grounding(url)
                if grounding_results:
                    comprehensive_results["grounding_comparison"][url_name] = grounding_results
        
        # Analyze configuration performance
        if comprehensive_results["url_results"]:
            comprehensive_results["configuration_performance"] = self._analyze_web_performance(
                comprehensive_results["url_results"]
            )
        
        # Save results
        self._save_test_results(comprehensive_results, "web_test_results.json")
        
        return comprehensive_results
    
    def _analyze_web_performance(self, url_results: Dict[str, Dict[str, RecognitionResult]]) -> Dict[str, Any]:
        """Analyze performance across different web configurations."""
        config_stats = {}
        
        for url_name, config_results in url_results.items():
            for config_name, result in config_results.items():
                if config_name not in config_stats:
                    config_stats[config_name] = {
                        "success_count": 0,
                        "total_tests": 0,
                        "total_time": 0,
                        "average_time": 0,
                        "success_rate": 0,
                        "network_errors": 0,
                        "api_errors": 0
                    }
                
                stats = config_stats[config_name]
                stats["total_tests"] += 1
                stats["total_time"] += result.processing_time
                
                if result.success:
                    stats["success_count"] += 1
                else:
                    # Categorize errors
                    if result.error_message and "network" in result.error_message.lower():
                        stats["network_errors"] += 1
                    else:
                        stats["api_errors"] += 1
        
        # Calculate averages and rates
        for config_name, stats in config_stats.items():
            if stats["total_tests"] > 0:
                stats["average_time"] = stats["total_time"] / stats["total_tests"]
                stats["success_rate"] = stats["success_count"] / stats["total_tests"]
        
        return config_stats
    
    def _save_test_results(self, results: Dict[str, Any], filename: str):
        """Save test results to JSON file."""
        try:
            output_path = os.path.join(self.output_dir, filename)
            
            # Convert RecognitionResult objects to dictionaries for JSON serialization
            serializable_results = self._make_serializable(results)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Web test results saved to {output_path}")
            
        except Exception as e:
            logger.error(f"Error saving web test results: {e}")
    
    def _make_serializable(self, data: Any) -> Any:
        """Convert data to JSON-serializable format."""
        if isinstance(data, RecognitionResult):
            return {
                "success": data.success,
                "analysis": data.analysis,
                "processing_time": data.processing_time,
                "config_used": data.config_used,
                "preprocessing_applied": data.preprocessing_applied,
                "image_info": data.image_info,
                "error_message": data.error_message
            }
        elif isinstance(data, dict):
            return {key: self._make_serializable(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._make_serializable(item) for item in data]
        else:
            return data
    
    def generate_web_report(self, results: Dict[str, Any]) -> str:
        """Generate a human-readable web test report."""
        report = []
        report.append("=" * 80)
        report.append("WEB IMAGE CURRENCY RECOGNITION TEST REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Test summary
        summary = results.get("test_summary", {})
        report.append(f"Test Date: {summary.get('test_timestamp', 'Unknown')}")
        report.append(f"Total URLs Tested: {summary.get('total_urls', 0)}")
        report.append("")
        
        # Configuration performance
        performance = results.get("configuration_performance", {})
        if performance:
            report.append("WEB CONFIGURATION PERFORMANCE SUMMARY")
            report.append("-" * 40)
            
            for config_name, stats in performance.items():
                report.append(f"Configuration: {config_name}")
                report.append(f"  Success Rate: {stats['success_rate']:.1%}")
                report.append(f"  Average Time: {stats['average_time']:.2f}s")
                report.append(f"  Network Errors: {stats['network_errors']}")
                report.append(f"  API Errors: {stats['api_errors']}")
                report.append(f"  Total Tests: {stats['total_tests']}")
                report.append("")
        
        # Streaming comparison
        streaming = results.get("streaming_comparison", {})
        if streaming:
            report.append("STREAMING VS NON-STREAMING COMPARISON")
            report.append("-" * 40)
            
            for url_name, comparison in streaming.items():
                report.append(f"URL: {url_name}")
                
                streaming_result = comparison.get("streaming")
                non_streaming_result = comparison.get("non_streaming")
                
                if streaming_result and non_streaming_result:
                    report.append(f"  Streaming: {'✓' if streaming_result.success else '✗'} ({streaming_result.processing_time:.2f}s)")
                    report.append(f"  Non-Streaming: {'✓' if non_streaming_result.success else '✗'} ({non_streaming_result.processing_time:.2f}s)")
                    
                    if streaming_result.success and non_streaming_result.success:
                        time_diff = streaming_result.processing_time - non_streaming_result.processing_time
                        if time_diff < 0:
                            report.append(f"  Streaming was {abs(time_diff):.2f}s faster")
                        else:
                            report.append(f"  Non-streaming was {time_diff:.2f}s faster")
                
                report.append("")
        
        # Grounding comparison
        grounding = results.get("grounding_comparison", {})
        if grounding:
            report.append("WEB SEARCH GROUNDING COMPARISON")
            report.append("-" * 40)
            
            for url_name, comparison in grounding.items():
                report.append(f"URL: {url_name}")
                
                no_grounding = comparison.get("no_grounding")
                with_grounding = comparison.get("with_grounding")
                
                if no_grounding and with_grounding:
                    report.append(f"  Without Grounding: {'✓' if no_grounding.success else '✗'} ({no_grounding.processing_time:.2f}s)")
                    report.append(f"  With Grounding: {'✓' if with_grounding.success else '✗'} ({with_grounding.processing_time:.2f}s)")
                    
                    if both_successful := (no_grounding.success and with_grounding.success):
                        report.append("  Web search grounding may provide additional context")
                
                report.append("")
        
        return "\n".join(report)
    
    def add_test_url(self, url: str) -> bool:
        """
        Add a URL to the test suite.
        
        Args:
            url: Image URL to add
            
        Returns:
            True if URL was added successfully
        """
        if self.validate_url(url):
            self.sample_urls.append(url)
            logger.info(f"Added test URL: {url}")
            return True
        else:
            logger.warning(f"Invalid URL not added: {url}")
            return False


def run_web_tests():
    """Main function to run web image tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Hong Kong currency recognition with web images")
    parser.add_argument("--urls", nargs='+', help="Image URLs to test")
    parser.add_argument("--url-file", help="File containing URLs (one per line)")
    parser.add_argument("--preset", default="balanced_web", help="Configuration preset to use")
    parser.add_argument("--api-key", help="Gemini API key (or set GEMINI_API_KEY env var)")
    parser.add_argument("--test-streaming", action="store_true", help="Test streaming vs non-streaming")
    parser.add_argument("--test-grounding", action="store_true", help="Test web search grounding")
    
    args = parser.parse_args()
    
    try:
        # Initialize tester
        tester = WebImageTester(args.api_key)
        
        # Collect URLs to test
        test_urls = []
        
        if args.urls:
            test_urls.extend(args.urls)
        
        if args.url_file:
            try:
                with open(args.url_file, 'r') as f:
                    file_urls = [line.strip() for line in f if line.strip()]
                test_urls.extend(file_urls)
            except FileNotFoundError:
                print(f"Error: URL file {args.url_file} not found")
                return
        
        if not test_urls:
            print("No URLs provided for testing. Use --urls or --url-file")
            return
        
        print(f"Testing {len(test_urls)} web image URLs")
        
        # Run tests based on arguments
        if args.test_streaming and len(test_urls) > 0:
            print("Running streaming comparison test...")
            results = tester.test_streaming_vs_nonstreaming(test_urls[0])
            
            for test_type, result in results.items():
                status = "✓ SUCCESS" if result.success else "✗ FAILED"
                print(f"{test_type}: {status} ({result.processing_time:.2f}s)")
        
        elif args.test_grounding and len(test_urls) > 0:
            print("Running web search grounding test...")
            results = tester.test_web_search_grounding(test_urls[0])
            
            for test_type, result in results.items():
                status = "✓ SUCCESS" if result.success else "✗ FAILED"
                print(f"{test_type}: {status} ({result.processing_time:.2f}s)")
        
        else:
            # Run comprehensive test suite
            print("Running comprehensive web test suite...")
            results = tester.test_different_image_sources(test_urls)
            
            # Generate and print report
            report = tester.generate_web_report(results)
            print(report)
            
            # Save report to file
            report_path = os.path.join(tester.output_dir, "web_test_report.txt")
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"\nDetailed report saved to: {report_path}")
    
    except Exception as e:
        logger.error(f"Web test execution failed: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    run_web_tests()