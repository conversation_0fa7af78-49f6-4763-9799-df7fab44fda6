# Hong Kong Currency Recognition Project Memory

## Project Overview
A comprehensive Hong Kong currency recognition system using Google's Gemini 2.5 Flash API with advanced image preprocessing capabilities.

## Key Implementation Details

### 1. Architecture
- **Modular Design**: Separated into image processing, Gemini API client, and recognition logic
- **Multiple Configuration Presets**: high_accuracy, balanced, fast_response, web_enhanced, conservative
- **Dual Support**: Both local images and web URLs
- **Response Modes**: Streaming and non-streaming API responses

### 2. Core Components

#### Image Processor (`src/image_processor.py`)
- Enhancement functions: contrast, brightness, saturation, sharpness
- Illumination normalization and noise reduction
- Three enhancement levels: minimal, full, web-optimized

#### Gemini Client (`src/gemini_client.py`)
- Configurable API settings with dataclass
- Five presets with different temperature, top_p, top_k settings
- JSON output format enforcement
- Web search grounding support

#### Currency Recognizer (`src/currency_recognizer.py`)
- RecognitionResult dataclass for structured results
- Validation functions for currency results
- Support for batch processing
- Integration of preprocessing and API calls

### 3. Testing Infrastructure
- `tests/test_local.py`: Comprehensive local image testing
- `tests/test_web.py`: Web image testing with URL validation
- Multiple configuration testing capabilities
- Detailed test report generation

### 4. Applications
- `currency_app.py`: Enhanced CLI with full feature support
- `main.py`: Original implementation preserved
- Support for single image, batch processing, preset testing
- Multiple output formats (JSON, text)

### 5. Configuration Presets

```python
# High Accuracy (Local Images)
temperature=0.1, top_p=0.8, top_k=10, max_tokens=1000

# Balanced (Web Images)
temperature=0.3, top_p=0.85, top_k=20, max_tokens=800

# Fast Response
temperature=0.4, top_p=0.9, top_k=30, max_tokens=500

# Web Enhanced
temperature=0.2, top_p=0.8, top_k=15, max_tokens=1200
enable_grounding=True

# Conservative
temperature=0.05, top_p=0.7, top_k=5, max_tokens=600
```

### 6. JSON Output Format
```json
{
  "紙幣": [
    {"面額": "X元", "數量": Y, "置信度": 0.XX}
  ],
  "硬幣": [
    {"面額": "X元", "數量": Y, "置信度": 0.XX}
  ],
  "無法識別": {
    "原因": "說明為何無法識別",
    "可見特徵": "描述能看到的特徵"
  }
}
```

### 7. Usage Commands

```bash
# Basic usage
uv run python currency_app.py --image hkbanknote.jpeg

# With specific preset
uv run python currency_app.py --image image.jpg --preset high_accuracy_local

# Web image
uv run python currency_app.py --url https://example.com/currency.jpg --preset web_enhanced

# Test all presets
uv run python currency_app.py --image test.jpg --test-all-presets

# Batch processing
uv run python currency_app.py --batch *.png --preset balanced
```

### 8. Dependencies (via uv)
- google-generativeai>=0.8.5
- pillow>=11.3.0
- numpy>=1.26.0
- requests>=2.31.0

### 9. Key Features Implemented
- ✅ Image preprocessing with multiple enhancement options
- ✅ Configurable Gemini API settings
- ✅ Streaming and non-streaming responses
- ✅ JSON output format
- ✅ Web search grounding
- ✅ Local and web image support
- ✅ Comprehensive testing suite
- ✅ CLI application with multiple options
- ✅ Batch processing capability
- ✅ Configuration preset system

### 10. Project Structure
```
MoneyRecognition/
├── src/
│   ├── image_processor.py      # Image enhancement module
│   ├── gemini_client.py        # Gemini API client
│   └── currency_recognizer.py  # Main recognition logic
├── tests/
│   ├── test_local.py           # Local image tests
│   └── test_web.py             # Web image tests
├── currency_app.py             # Enhanced CLI application
├── main.py                     # Original implementation
├── prompt.md                   # HK currency recognition prompt
├── Schedule.md                 # Implementation plan
├── pyproject.toml              # uv dependencies
└── README.md                   # Project documentation
```

## Notes
- Project uses `uv` for Python dependency management
- All requested features have been successfully implemented
- System tested and verified working with sample images
- Ready for production use with Hong Kong banknote recognition