"""
Enhanced Hong Kong Currency Recognition Application.
Provides comprehensive currency recognition with advanced preprocessing and configuration options.
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from currency_recognizer import CurrencyRecognizer


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="Enhanced Hong Kong Currency Recognition System using Gemini 2.5 Flash API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Recognize currency from local image
  python currency_app.py --image path/to/image.jpg
  
  # Use specific preset configuration
  python currency_app.py --image image.jpg --preset high_accuracy_local
  
  # Disable preprocessing
  python currency_app.py --image image.jpg --no-preprocessing
  
  # Recognize from web image URL
  python currency_app.py --url https://example.com/currency.jpg --preset web_enhanced
  
  # Test all configurations
  python currency_app.py --image test.jpg --test-all-presets
        """
    )
    
    # Input options
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument("--image", help="Path to local image file")
    input_group.add_argument("--url", help="URL of web image")
    input_group.add_argument("--batch", nargs='+', help="Multiple image paths for batch processing")
    
    # Configuration options
    parser.add_argument("--preset", default="high_accuracy_local", 
                       help="Configuration preset (default: high_accuracy_local)")
    parser.add_argument("--no-preprocessing", action="store_true", 
                       help="Disable image preprocessing")
    parser.add_argument("--api-key", 
                       help="Gemini API key (or set GEMINI_API_KEY env var)")
    
    # Output options
    parser.add_argument("--output-format", choices=["json", "text", "both"], default="text",
                       help="Output format (default: text)")
    parser.add_argument("--save-processed", action="store_true",
                       help="Save processed image")
    parser.add_argument("--output-dir", default="output",
                       help="Output directory for processed images and results")
    
    # Testing options
    parser.add_argument("--test-all-presets", action="store_true",
                       help="Test all available presets with the input image")
    parser.add_argument("--compare-preprocessing", action="store_true",
                       help="Compare results with and without preprocessing")
    
    # Utility options
    parser.add_argument("--list-presets", action="store_true",
                       help="List available configuration presets")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose output")
    
    args = parser.parse_args()
    
    # Set up logging
    import logging
    logging.basicConfig(
        level=logging.DEBUG if args.verbose else logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Initialize recognizer
        recognizer = CurrencyRecognizer(args.api_key)
        
        # Handle list presets
        if args.list_presets:
            presets = recognizer.get_available_presets()
            print("Available configuration presets:")
            for preset in presets:
                print(f"  - {preset}")
            return
        
        # Create output directory
        os.makedirs(args.output_dir, exist_ok=True)
        
        # Handle different input types
        if args.image:
            handle_single_image(recognizer, args)
        elif args.url:
            handle_web_image(recognizer, args)
        elif args.batch:
            handle_batch_images(recognizer, args)
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def handle_single_image(recognizer, args):
    """Handle single image recognition."""
    if not os.path.exists(args.image):
        print(f"Error: Image file {args.image} not found")
        return
    
    print(f"Analyzing image: {args.image}")
    
    if args.test_all_presets:
        # Test all presets
        print("Testing all available presets...")
        results = recognizer.test_all_presets(args.image)
        
        if args.output_format in ["json", "both"]:
            output_path = os.path.join(args.output_dir, "all_presets_results.json")
            save_results_json(results, output_path)
        
        if args.output_format in ["text", "both"]:
            print_preset_comparison(results)
    
    elif args.compare_preprocessing:
        # Compare preprocessing effects
        print("Comparing preprocessing effects...")
        results = recognizer.compare_preprocessing_effects(args.image, args.preset)
        
        if args.output_format in ["json", "both"]:
            output_path = os.path.join(args.output_dir, "preprocessing_comparison.json")
            save_results_json(results, output_path)
        
        if args.output_format in ["text", "both"]:
            print_preprocessing_comparison(results)
    
    else:
        # Single recognition
        result = recognizer.recognize_currency(
            args.image,
            preset_name=args.preset,
            enable_preprocessing=not args.no_preprocessing,
            save_processed_image=args.save_processed,
            output_dir=args.output_dir
        )
        
        if args.output_format in ["json", "both"]:
            output_path = os.path.join(args.output_dir, "recognition_result.json")
            save_result_json(result, output_path)
        
        if args.output_format in ["text", "both"]:
            print_single_result(result, recognizer)


def handle_web_image(recognizer, args):
    """Handle web image recognition."""
    print(f"Analyzing web image: {args.url}")
    
    # Use web-optimized preset if not specified
    preset = args.preset if args.preset != "high_accuracy_local" else "balanced_web"
    
    result = recognizer.recognize_currency(
        args.url,
        preset_name=preset,
        enable_preprocessing=not args.no_preprocessing,
        save_processed_image=args.save_processed,
        output_dir=args.output_dir
    )
    
    if args.output_format in ["json", "both"]:
        output_path = os.path.join(args.output_dir, "web_recognition_result.json")
        save_result_json(result, output_path)
    
    if args.output_format in ["text", "both"]:
        print_single_result(result, recognizer)


def handle_batch_images(recognizer, args):
    """Handle batch image processing."""
    print(f"Processing {len(args.batch)} images in batch mode...")
    
    results = recognizer.batch_recognize(
        args.batch,
        preset_name=args.preset,
        enable_preprocessing=not args.no_preprocessing
    )
    
    if args.output_format in ["json", "both"]:
        output_path = os.path.join(args.output_dir, "batch_results.json")
        save_batch_results_json(results, args.batch, output_path)
    
    if args.output_format in ["text", "both"]:
        print_batch_results(results, args.batch, recognizer)


def print_single_result(result, recognizer):
    """Print single recognition result in text format."""
    print("\n" + "="*60)
    print("CURRENCY RECOGNITION RESULT")
    print("="*60)
    
    print(f"Status: {'✓ SUCCESS' if result.success else '✗ FAILED'}")
    print(f"Processing Time: {result.processing_time:.2f} seconds")
    print(f"Configuration: {result.config_used}")
    print(f"Preprocessing: {'Enabled' if result.preprocessing_applied else 'Disabled'}")
    
    if result.error_message:
        print(f"Error: {result.error_message}")
        return
    
    if result.success and result.analysis:
        print("\nCurrency Analysis:")
        print("-" * 30)
        
        # Print banknotes
        banknotes = result.analysis.get("紙幣", [])
        if banknotes:
            print("Banknotes:")
            total_banknote_value = 0
            for item in banknotes:
                denomination = item.get("面額", "Unknown")
                quantity = item.get("數量", 0)
                confidence = item.get("置信度", 0)
                print(f"  {denomination}: {quantity} × (confidence: {confidence:.2f})")
                
                try:
                    value = float(denomination.replace("元", ""))
                    total_banknote_value += value * quantity
                except:
                    pass
            
            print(f"  Total banknote value: HK${total_banknote_value}")
        
        # Print coins
        coins = result.analysis.get("硬幣", [])
        if coins:
            print("\nCoins:")
            total_coin_value = 0
            for item in coins:
                denomination = item.get("面額", "Unknown")
                quantity = item.get("數量", 0)
                confidence = item.get("置信度", 0)
                print(f"  {denomination}: {quantity} × (confidence: {confidence:.2f})")
                
                try:
                    value = float(denomination.replace("元", ""))
                    total_coin_value += value * quantity
                except:
                    pass
            
            print(f"  Total coin value: HK${total_coin_value}")
        
        # Print unidentified items
        unidentified = result.analysis.get("無法識別", {})
        if unidentified:
            print(f"\nUnidentified items: {unidentified}")
    
    # Validate results
    if result.success and result.analysis:
        validation = recognizer.validate_currency_result(result.analysis)
        print(f"\nValidation:")
        print(f"  Total items: {validation['total_banknotes'] + validation['total_coins']}")
        print(f"  Total value: HK${validation['total_value']:.2f}")
        print(f"  Average confidence: {validation['average_confidence']:.2f}")
        
        if validation['warnings']:
            print("  Warnings:")
            for warning in validation['warnings']:
                print(f"    - {warning}")


def print_preset_comparison(results):
    """Print comparison of all presets."""
    print("\n" + "="*80)
    print("PRESET COMPARISON RESULTS")
    print("="*80)
    
    for preset_name, result in results.items():
        status = "✓ SUCCESS" if result.success else "✗ FAILED"
        print(f"\n{preset_name}: {status}")
        print(f"  Processing Time: {result.processing_time:.2f}s")
        print(f"  Preprocessing: {'Yes' if result.preprocessing_applied else 'No'}")
        
        if result.error_message:
            print(f"  Error: {result.error_message}")
        elif result.success:
            # Quick validation
            banknotes = result.analysis.get("紙幣", [])
            coins = result.analysis.get("硬幣", [])
            total_items = len(banknotes) + len(coins)
            print(f"  Total Items Found: {total_items}")


def print_preprocessing_comparison(results):
    """Print preprocessing comparison results."""
    print("\n" + "="*60)
    print("PREPROCESSING COMPARISON")
    print("="*60)
    
    no_prep = results.get("no_preprocessing")
    with_prep = results.get("with_preprocessing")
    
    if no_prep:
        status = "✓ SUCCESS" if no_prep.success else "✗ FAILED"
        print(f"Without Preprocessing: {status} ({no_prep.processing_time:.2f}s)")
    
    if with_prep:
        status = "✓ SUCCESS" if with_prep.success else "✗ FAILED"
        print(f"With Preprocessing: {status} ({with_prep.processing_time:.2f}s)")
    
    if no_prep and with_prep and (no_prep.success and with_prep.success):
        time_diff = with_prep.processing_time - no_prep.processing_time
        if time_diff > 0:
            print(f"Preprocessing added {time_diff:.2f}s processing time")
        else:
            print(f"Preprocessing saved {abs(time_diff):.2f}s processing time")


def print_batch_results(results, image_paths, recognizer):
    """Print batch processing results."""
    print("\n" + "="*60)
    print("BATCH PROCESSING RESULTS")
    print("="*60)
    
    successful = sum(1 for result in results if result.success)
    total = len(results)
    
    print(f"Success Rate: {successful}/{total} ({successful/total*100:.1f}%)")
    print(f"Average Processing Time: {sum(r.processing_time for r in results)/total:.2f}s")
    
    print("\nIndividual Results:")
    for i, (path, result) in enumerate(zip(image_paths, results)):
        status = "✓" if result.success else "✗"
        filename = os.path.basename(path)
        print(f"  {status} {filename} ({result.processing_time:.2f}s)")


def save_result_json(result, output_path):
    """Save single result to JSON file."""
    result_dict = {
        "success": result.success,
        "analysis": result.analysis,
        "processing_time": result.processing_time,
        "config_used": result.config_used,
        "preprocessing_applied": result.preprocessing_applied,
        "image_info": result.image_info,
        "error_message": result.error_message,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result_dict, f, indent=2, ensure_ascii=False)
    
    print(f"Results saved to: {output_path}")


def save_results_json(results, output_path):
    """Save multiple results to JSON file."""
    results_dict = {}
    for key, result in results.items():
        results_dict[key] = {
            "success": result.success,
            "analysis": result.analysis,
            "processing_time": result.processing_time,
            "config_used": result.config_used,
            "preprocessing_applied": result.preprocessing_applied,
            "image_info": result.image_info,
            "error_message": result.error_message
        }
    
    output_data = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "results": results_dict
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"Results saved to: {output_path}")


def save_batch_results_json(results, image_paths, output_path):
    """Save batch results to JSON file."""
    batch_data = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "total_images": len(results),
        "successful": sum(1 for r in results if r.success),
        "results": {}
    }
    
    for i, (path, result) in enumerate(zip(image_paths, results)):
        filename = os.path.basename(path)
        batch_data["results"][filename] = {
            "success": result.success,
            "analysis": result.analysis,
            "processing_time": result.processing_time,
            "config_used": result.config_used,
            "preprocessing_applied": result.preprocessing_applied,
            "image_info": result.image_info,
            "error_message": result.error_message
        }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(batch_data, f, indent=2, ensure_ascii=False)
    
    print(f"Batch results saved to: {output_path}")


if __name__ == "__main__":
    main()