# Hong Kong Currency Recognition Project Schedule

## Project Overview
Develop a Hong Kong currency recognition system using Python and Gemini 2.5 Flash API vision recognition to identify banknotes and coins from images.

## Core Features
- Image preprocessing (contrast, illumination, saturation enhancement)
- Gemini API integration with configurable parameters
- Support for local and web image sources
- JSON output with denomination, quantity, and confidence scores
- Recognition of both 2018 and 2010 series banknotes

## Implementation Plan

### Phase 1: Core Infrastructure

#### TODO
- [ ] Set up project dependencies (google-generativeai, Pillow, requests)
- [ ] Create image preprocessing module with enhancement functions
- [ ] Design Gemini API configuration system with multiple presets
- [ ] Implement base currency recognition class

### Phase 2: Image Processing Functions

#### TODO  
- [ ] Implement contrast enhancement function
- [ ] Implement illumination normalization function
- [ ] Implement saturation adjustment function
- [ ] Create image quality assessment functions
- [ ] Add support for both local and web image loading

### Phase 3: Gemini API Integration

#### TODO
- [ ] Configure Gemini API with JSON output format
- [ ] Implement streaming response handling
- [ ] Set up safety settings for content filtering
- [ ] Configure temperature, top_p, top_k parameters
- [ ] Add web search grounding capabilities
- [ ] Create different API preset configurations

### Phase 4: Testing Infrastructure

#### TODO
- [ ] Create local image test runner with preprocessing options
- [ ] Create web image test runner with different API configurations
- [ ] Implement test result comparison and validation
- [ ] Add performance metrics tracking
- [ ] Create sample test cases with expected outputs

## API Configuration Presets

### Preset 1: High Accuracy (Local Images)
- Temperature: 0.1
- Top_p: 0.8
- Top_k: 10
- Max_tokens: 1000
- Streaming: False
- Preprocessing: Full enhancement

### Preset 2: Balanced (Web Images) 
- Temperature: 0.3
- Top_p: 0.9
- Top_k: 20
- Max_tokens: 800
- Streaming: True
- Preprocessing: Selective enhancement

### Preset 3: Fast Response
- Temperature: 0.5
- Top_p: 0.95
- Top_k: 40
- Max_tokens: 500
- Streaming: True
- Preprocessing: Minimal

## Testing Strategy

### Local Image Tests
1. Test with original images (no preprocessing)
2. Test with contrast enhancement only
3. Test with full preprocessing pipeline
4. Compare accuracy across different API presets

### Web Image Tests
1. Test with various image URLs
2. Test streaming vs non-streaming responses
3. Test with web search grounding enabled/disabled
4. Measure response time and accuracy

## Success Criteria
- [ ] Accurate recognition of HK banknotes (>95% for clear images)
- [ ] Proper handling of multiple currencies in single image
- [ ] Robust error handling for unclear/fake currency
- [ ] Response time under 5 seconds for standard images
- [ ] Comprehensive test coverage for both local and web sources

## File Structure
```
MoneyRecognition/
├── main.py                 # Main application entry point
├── src/
│   ├── image_processor.py  # Image preprocessing functions
│   ├── gemini_client.py    # Gemini API client with configurations
│   ├── currency_recognizer.py # Main recognition logic
│   └── utils.py           # Helper utilities
├── tests/
│   ├── test_local.py      # Local image testing
│   └── test_web.py        # Web image testing
├── config/
│   └── api_presets.json   # Gemini API configuration presets
└── samples/               # Sample test images
```

## Current Status: Planning Complete
## Next Phase: Begin Implementation

### DOING
- Creating Schedule.md (Current)

### DONE
- Project analysis and planning
- Requirements understanding
- Prompt analysis