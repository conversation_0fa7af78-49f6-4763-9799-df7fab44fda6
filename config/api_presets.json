{"presets": {"high_accuracy_local": {"description": "High accuracy configuration for local images with clear quality", "temperature": 0.1, "top_p": 0.8, "top_k": 10, "max_output_tokens": 1000, "enable_streaming": false, "enable_web_search": false, "image_preprocessing": {"enhancement_level": "full", "contrast": 1.2, "brightness": 1.1, "saturation": 1.15, "sharpness": 1.1, "normalize_illumination": true, "reduce_noise": true}}, "balanced_web": {"description": "Balanced configuration for web images with moderate enhancement", "temperature": 0.3, "top_p": 0.9, "top_k": 20, "max_output_tokens": 800, "enable_streaming": true, "enable_web_search": false, "image_preprocessing": {"enhancement_level": "web", "contrast": 1.3, "brightness": 1.2, "saturation": 1.2, "sharpness": 1.0, "normalize_illumination": true, "reduce_noise": true}}, "fast_response": {"description": "Fast response configuration with minimal processing", "temperature": 0.5, "top_p": 0.95, "top_k": 40, "max_output_tokens": 500, "enable_streaming": true, "enable_web_search": false, "image_preprocessing": {"enhancement_level": "minimal", "contrast": 1.1, "brightness": 1.0, "saturation": 1.0, "sharpness": 1.0, "normalize_illumination": false, "reduce_noise": false}}, "web_enhanced": {"description": "Web-enhanced configuration with search grounding", "temperature": 0.2, "top_p": 0.85, "top_k": 15, "max_output_tokens": 1000, "enable_streaming": false, "enable_web_search": true, "image_preprocessing": {"enhancement_level": "web", "contrast": 1.25, "brightness": 1.15, "saturation": 1.2, "sharpness": 1.1, "normalize_illumination": true, "reduce_noise": true}}, "conservative": {"description": "Conservative configuration with strict safety settings", "temperature": 0.05, "top_p": 0.7, "top_k": 5, "max_output_tokens": 1200, "enable_streaming": false, "enable_web_search": false, "image_preprocessing": {"enhancement_level": "full", "contrast": 1.15, "brightness": 1.05, "saturation": 1.1, "sharpness": 1.05, "normalize_illumination": true, "reduce_noise": true}}}, "test_configurations": {"local_image_tests": [{"name": "original_no_preprocessing", "preset": "high_accuracy_local", "preprocessing_enabled": false, "description": "Test with original image, no preprocessing"}, {"name": "full_preprocessing", "preset": "high_accuracy_local", "preprocessing_enabled": true, "description": "Test with full preprocessing pipeline"}, {"name": "balanced_with_preprocessing", "preset": "balanced_web", "preprocessing_enabled": true, "description": "Test with balanced settings and preprocessing"}], "web_image_tests": [{"name": "web_enhanced_streaming", "preset": "web_enhanced", "preprocessing_enabled": true, "description": "Web images with enhanced settings and streaming"}, {"name": "fast_response_minimal", "preset": "fast_response", "preprocessing_enabled": false, "description": "Fast response with minimal processing"}, {"name": "balanced_web_full", "preset": "balanced_web", "preprocessing_enabled": true, "description": "Balanced web configuration with full preprocessing"}]}}