# Hong Kong Currency Recognition System

A comprehensive Hong Kong currency recognition system using Google's Gemini 2.5 Flash API with advanced image preprocessing capabilities.

## Features

- **Advanced Image Preprocessing**: Contrast enhancement, illumination normalization, saturation adjustment
- **Multiple API Configurations**: High accuracy, balanced, fast response, and web-optimized presets
- **Local & Web Image Support**: Process images from local files or web URLs
- **Streaming & Non-Streaming**: Configurable response modes for different use cases
- **Comprehensive Testing**: Test suites for both local and web images
- **JSON Output**: Structured output with confidence scores and detailed analysis

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up your Gemini API key:
```bash
export GEMINI_API_KEY="your_api_key_here"
```

## Quick Start

### Basic Usage

```bash
# Recognize currency from local image
python currency_app.py --image path/to/image.jpg

# Recognize from web URL
python currency_app.py --url https://example.com/currency.jpg
```

### Testing

```bash
# Test local images
python tests/test_local.py --single-image image.jpg

# Test web images  
python tests/test_web.py --urls https://example.com/image.jpg
```

## Configuration Presets

- **high_accuracy_local**: High accuracy for local images
- **balanced_web**: Balanced configuration for web images
- **fast_response**: Fast response with minimal processing
- **web_enhanced**: Web-enhanced with search grounding

## Project Structure

```
MoneyRecognition/
   src/                    # Core modules
   tests/                  # Test suites
   config/                 # Configuration files
   currency_app.py         # Enhanced main application
   main.py                # Original implementation
```