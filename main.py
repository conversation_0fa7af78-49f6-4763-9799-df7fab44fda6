# -*- coding: utf-8 -*-

import os
import google.generativeai as genai
from PIL import Image
import sys

# --- 前置設定 ---
# 1. 安裝必要的函式庫
#    pip install google-generativeai Pillow

# 2. 設定您的 Google AI API 金鑰
#    強烈建議使用環境變數來管理金鑰。
try:
    api_key = os.environ.get("GOOGLE_API_KEY")
    if not api_key:
        api_key = "AIzaSyCe9CqQk6PzXhrLbF1XbGap-iypsT5koLs"  # 如果沒有設定環境變數，請將金鑰貼在這裡
        if api_key == "YOUR_API_KEY":
            print("錯誤：請務必將 'YOUR_API_KEY' 替換成您自己的 Google AI API 金鑰。")
            sys.exit(1) # 終止程式
    genai.configure(api_key=api_key)
except Exception as e:
    print(f"API 金鑰設定失敗，請檢查您的金鑰是否正確。錯誤訊息：{e}")
    sys.exit(1)


def identify_banknote_stream(image_path, prompt):
    """
    使用 Gemini 2.0 Flash 模型，以流式方式識別本地圖片中的紙幣。

    Args:
        image_path (str): 本地圖片的檔案路徑。
        prompt (str): 您想對模型提出的問題或指令。

    Returns:
        A generator object that yields the model's response chunks.
        如果發生錯誤，則會印出錯誤訊息並返回 None。
    """
    # --- 步驟 1: 檢查檔案是否存在 ---
    if not os.path.exists(image_path):
        print(f"錯誤：找不到指定的圖片檔案。請檢查路徑是否正確：'{image_path}'")
        return None
    
    print("正在初始化模型並讀取圖片...")
    model = genai.GenerativeModel('gemini-2.5-flash')

    try:
        img = Image.open(image_path)
        
        # --- 步驟 2: 發送流式請求 ---
        # 關鍵參數 stream=True，這會讓 API 以資料流的方式回傳結果
        print("圖片讀取成功，正在向 Gemini API 發送流式請求...")
        print("-" * 30) # 分隔線
        
        response = model.generate_content([prompt, img], stream=True)
        return response

    except Exception as e:
        print(f"處理過程中發生未預期的錯誤：{e}")
        return None


def search_and_answer(question, use_stream=False):
    """
    使用 Gemini 模型結合 Google Search 工具來搜索並回答問題。
    
    Args:
        question (str): 您想要搜索和詢問的問題。
        use_stream (bool): 是否使用流式回應，預設為 False。
        
    Returns:
        如果 use_stream=False，返回完整的回應文字。
        如果 use_stream=True，返回生成器物件用於流式輸出。
        如果發生錯誤，則會印出錯誤訊息並返回 None。
    """
    print(f"正在搜索問題：{question}")
    print("正在初始化 Gemini 模型...")
    
    try:
        # 初始化模型
        model = genai.GenerativeModel('gemini-2.5-flash')
        
        # 嘗試使用搜索功能
        try:
            tools = [{"google_search_retrieval": {
                "dynamic_retrieval_config": {
                    "mode": "MODE_DYNAMIC",
                    "dynamic_threshold": 0.3
                }
            }}]
            
            print("正在向 Gemini API 發送搜索請求...")
            print("-" * 50)
            
            # 發送請求
            if use_stream:
                response = model.generate_content(question, tools=tools, stream=True)
                return response
            else:
                response = model.generate_content(question, tools=tools)
                return response.text
                
        except Exception as search_error:
            print(f"搜索功能不可用：{search_error}")
            print("改用基礎模型回答...")
            
            # 備用方案
            enhanced_question = f"{question}\n\n[註：請基於您的知識回答，並說明回答的依據和限制]"
            
            if use_stream:
                response = model.generate_content(enhanced_question, stream=True)
                return response
            else:
                response = model.generate_content(enhanced_question)
                return response.text + "\n\n[註：此回答未使用網絡搜索，基於模型訓練資料]"
            
    except Exception as e:
        print(f"搜索過程中發生未預期的錯誤：{e}")
        return None


def search_and_answer_with_citations(question):
    """
    使用 Gemini 模型結合 Google Search 工具來搜索並回答問題，並提供引用資訊。
    
    Args:
        question (str): 您想要搜索和詢問的問題。
        
    Returns:
        dict: 包含回應文字、搜索查詢、引用來源等資訊的字典。
        如果發生錯誤，則會印出錯誤訊息並返回 None。
    """
    print(f"正在搜索問題：{question}")
    print("正在初始化 Gemini 模型...")
    
    try:
        # 初始化模型
        model = genai.GenerativeModel('gemini-2.5-flash')
        
        # 嘗試使用搜索功能
        try:
            tools = [{"google_search_retrieval": {
                "dynamic_retrieval_config": {
                    "mode": "MODE_DYNAMIC",
                    "dynamic_threshold": 0.3
                }
            }}]
            
            print("正在向 Gemini API 發送搜索請求...")
            print("-" * 50)
            
            response = model.generate_content(question, tools=tools)
            
            # 提取回應資訊
            result = {
                'answer': response.text,
                'search_queries': [],
                'sources': [],
                'citations': [],
                'used_search': False
            }
            
            # 檢查是否有 grounding metadata
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                
                if hasattr(candidate, 'grounding_metadata') and candidate.grounding_metadata:
                    result['used_search'] = True
                    grounding_metadata = candidate.grounding_metadata
                    
                    # 搜索查詢
                    if hasattr(grounding_metadata, 'web_search_queries'):
                        result['search_queries'] = grounding_metadata.web_search_queries
                    
                    # 引用來源
                    if hasattr(grounding_metadata, 'grounding_chunks'):
                        for chunk in grounding_metadata.grounding_chunks:
                            if hasattr(chunk, 'web'):
                                result['sources'].append({
                                    'title': chunk.web.title,
                                    'uri': chunk.web.uri
                                })
            
            return result
            
        except Exception as search_error:
            print(f"搜索功能不可用：{search_error}")
            print("改用基礎模型回答...")
            
            # 備用方案
            enhanced_question = f"{question}\n\n[註：請基於您的知識回答，並說明回答的依據和限制]"
            response = model.generate_content(enhanced_question)
            
            result = {
                'answer': response.text + "\n\n[註：此回答未使用網絡搜索，基於模型訓練資料]",
                'search_queries': [],
                'sources': [],
                'citations': [],
                'used_search': False,
                'search_error': str(search_error)
            }
            
            return result
        
    except Exception as e:
        print(f"搜索過程中發生未預期的錯誤：{e}")
        return None


def identify_banknote_with_search(image_path, prompt):
    """
    使用 Gemini 模型結合 Google Search 工具來識別香港紙幣，提供更準確和最新的識別結果。
    
    Args:
        image_path (str): 本地圖片的檔案路徑。
        prompt (str): 您想對模型提出的問題或指令。
        
    Returns:
        dict: 包含識別結果、搜索資訊和引用來源的字典。
        如果發生錯誤，則會印出錯誤訊息並返回 None。
    """
    # --- 步驟 1: 檢查檔案是否存在 ---
    if not os.path.exists(image_path):
        print(f"錯誤：找不到指定的圖片檔案。請檢查路徑是否正確：'{image_path}'")
        return None
    
    print("正在初始化模型並讀取圖片（嘗試網絡搜索輔助）...")
    
    try:
        # 初始化模型
        model = genai.GenerativeModel('gemini-2.5-flash') 
        
        # 讀取圖片
        img = Image.open(image_path)
        
        # 修改提示詞，加入搜索輔助指令
        enhanced_prompt = f"""
{prompt}

**額外指令：**
請基於您的知識提供識別結果，並在回答中註明：
1. 識別的依據和特徵
2. 置信度評估
3. 如果需要最新資訊驗證，請說明需要查證的要點

請在識別結果中說明您的判斷依據。
"""
        
        print("圖片讀取成功，正在向 Gemini API 發送識別請求...")
        print("-" * 50)
        
        # 嘗試使用搜索功能
        try:
            # 嘗試使用 google_search_retrieval 工具
            tools = [{"google_search_retrieval": {
                "dynamic_retrieval_config": {
                    "mode": "MODE_DYNAMIC",
                    "dynamic_threshold": 0.3
                }
            }}]
            
            response = model.generate_content(
                [enhanced_prompt, img],
                tools=tools
            )
            
            # 提取回應資訊
            result = {
                'identification_result': response.text,
                'used_search': False,
                'search_queries': [],
                'sources': [],
                'citations': [],
                'method': 'search_enabled'
            }
            
            # 檢查是否有 grounding metadata（表示使用了搜索）
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                
                if hasattr(candidate, 'grounding_metadata') and candidate.grounding_metadata:
                    result['used_search'] = True
                    grounding_metadata = candidate.grounding_metadata
                    
                    # 搜索查詢
                    if hasattr(grounding_metadata, 'web_search_queries'):
                        result['search_queries'] = grounding_metadata.web_search_queries
                    
                    # 引用來源
                    if hasattr(grounding_metadata, 'grounding_chunks'):
                        for chunk in grounding_metadata.grounding_chunks:
                            if hasattr(chunk, 'web'):
                                result['sources'].append({
                                    'title': chunk.web.title,
                                    'uri': chunk.web.uri
                                })
            
            return result
            
        except Exception as search_error:
            print(f"搜索功能不可用：{search_error}")
            print("改用基礎模型進行識別...")
            
            # 備用方案：使用基礎模型
            fallback_response = model.generate_content([enhanced_prompt, img])
            
            result = {
                'identification_result': fallback_response.text + "\n\n[註：此結果未使用網絡搜索輔助，基於模型訓練資料]",
                'used_search': False,
                'search_queries': [],
                'sources': [],
                'citations': [],
                'method': 'fallback_no_search',
                'search_error': str(search_error)
            }
            
            return result
        
    except Exception as e:
        print(f"識別過程中發生未預期的錯誤：{e}")
        return None


# --- 主程式執行區塊 ---
if __name__ == "__main__":
    # --- 請修改這裡 ---
    # 請將此路徑替換成您電腦上真實的香港紙幣圖片檔案路徑
    # 範例路徑:
    # Windows: "C:\\Users\\<USER>\\Pictures\\hk_banknote.jpg" (注意雙反斜線)
    # macOS / Linux: "/home/<USER>/pictures/hk_banknote.jpg"
    local_image_path = "./hkbanknote2.png" 
    
    # --- 您提供的專業級香港紙幣識別提示 (Prompt) ---
    hk_banknote_prompt = """
你現在是一位專業的香港紙幣識別專家。請嚴格遵循以下規則來識別圖像中的物體。

**輸出格式：**
1.如果只檢測到一種面額的紙幣，請使用格式："檢測到[面額]紙幣"(例如：檢測到100元紙幣)
2.如果檢測到多種面額的紙幣，請使用格式："檢測到多張紙幣:[面額]紙幣、[面額]紙幣、[面額]紙幣"(例如：檢測到多張紙幣:100元紙幣、20元紙幣)
3.如果所有結果皆無法識別,請輸出："無法識別"

**香港紙幣特徵參考：**
1. 面額與顏色：10元(舊式綠色，新式紫色)、20元(藍色)、50元(舊式紫色，新式綠色)、100元(紅色)、500元(棕色)、1000元(金黃色)。
2. 中英文版本發鈔銀行：匯豐銀行、渣打銀行、中國銀行（香港）。
3. 防偽特徵：高透光水印、動感光亮圖案、唯一編號。

**識別流程:**
1.  識別排除項：首先，判斷圖像中是否包含假鈔(印有"SPECIMEN"、"樣本"等字樣)，冥鈔(印有"冥通銀行"、"地府銀行"、"天地銀行"等字樣)，或玩具鈔票、優惠卷。
2.  識別香港紙幣：對於非排除項，請結合紙幣的顏色、面額數字、銀行中英文名稱、防偽特徵等進行綜合判斷。圖像可能不完整或有遮擋，允許某些特徵缺失。
3.  識別所有紙幣類別：識別圖像中出現的**所有**不同面額的紙幣。(例如，如果圖像中有三張100元和一張20元，你只需要識別出100元和20元這兩個類別)。

請開始分析圖片。
"""
    
    print("=" * 80)
    print("香港紙幣識別系統 - 比較測試")
    print("=" * 80)
    
    # === 第一部分：原始快速識別 ===
    print("\n" + "=" * 50)
    print("【方法一】原始快速識別（不使用網絡搜索）")
    print("=" * 50)
    
    response_stream = identify_banknote_stream(local_image_path, hk_banknote_prompt)
    
    original_result = ""
    if response_stream:
        print(">>> 識別結果：")
        try:
            for chunk in response_stream:
                print(chunk.text, end="", flush=True)
                original_result += chunk.text
        except Exception as e:
            print(f"\n接收資料流時發生錯誤: {e}")
        print("\n" + "-" * 30)
    
    # === 第二部分：網絡搜索輔助識別 ===
    print("\n" + "=" * 50)
    print("【方法二】網絡搜索輔助識別")
    print("=" * 50)
    
    search_result = identify_banknote_with_search(local_image_path, hk_banknote_prompt)
    
    if search_result:
        print(">>> 識別結果：")
        print(search_result['identification_result'])
        
        print(f"\n>>> 是否使用了網絡搜索：{'是' if search_result['used_search'] else '否'}")
        
        if search_result['used_search']:
            if search_result['search_queries']:
                print(f">>> 搜索查詢：{search_result['search_queries']}")
            
            if search_result['sources']:
                print(">>> 參考來源：")
                for i, source in enumerate(search_result['sources'], 1):
                    print(f"  {i}. {source['title']}")
                    print(f"     網址：{source['uri']}")
        
        print("-" * 50)
    
    # === 第三部分：結果比較分析 ===
    print("\n" + "=" * 50)
    print("【結果比較分析】")
    print("=" * 50)
    
    print(">>> 方法一（快速識別）結果：")
    print(f"  {original_result.strip()}")
    
    if search_result:
        print("\n>>> 方法二（搜索輔助）結果：")
        print(f"  {search_result['identification_result'].strip()}")
        
        print(f"\n>>> 搜索輔助狀態：{'已使用網絡搜索' if search_result['used_search'] else '未使用網絡搜索'}")
        
        if search_result['used_search']:
            print(">>> 搜索輔助提供的額外資訊：")
            print("  - 獲取了最新的香港紙幣防偽特徵")
            print("  - 驗證了紙幣的真實性和準確性")
            print("  - 提供了官方權威來源的參考資料")
    
    print("\n>>> 比較總結：")
    print("  方法一：快速、基於模型訓練資料，適合基本識別")
    print("  方法二：準確、基於最新資訊，適合需要驗證的場景")
    
    print("\n" + "=" * 80)
    print("測試完成！")
    print("=" * 80)
