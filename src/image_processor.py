"""
Image preprocessing module for Hong Kong currency recognition.
Provides functions for enhancing image quality before API analysis.
"""

from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import numpy as np
import requests
from io import BytesIO
from typing import Union, Tuple, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageProcessor:
    """Enhanced image preprocessing for currency recognition."""
    
    def __init__(self):
        self.default_settings = {
            'contrast': 1.2,
            'brightness': 1.1,
            'saturation': 1.15,
            'sharpness': 1.1,
            'target_size': (1024, 768),
            'quality': 95
        }
    
    def load_image(self, source: Union[str, bytes]) -> Image.Image:
        """
        Load image from file path, URL, or bytes.
        
        Args:
            source: File path, URL, or bytes data
            
        Returns:
            PIL Image object
        """
        try:
            if isinstance(source, bytes):
                return Image.open(BytesIO(source))
            elif source.startswith(('http://', 'https://')):
                response = requests.get(source, timeout=10)
                response.raise_for_status()
                return Image.open(BytesIO(response.content))
            else:
                return Image.open(source)
        except Exception as e:
            logger.error(f"Error loading image from {source}: {e}")
            raise
    
    def enhance_contrast(self, image: Image.Image, factor: float = 1.2) -> Image.Image:
        """
        Enhance image contrast for better currency feature visibility.
        
        Args:
            image: Input PIL Image
            factor: Contrast enhancement factor (1.0 = no change)
            
        Returns:
            Enhanced PIL Image
        """
        if factor <= 0:
            raise ValueError("Contrast factor must be positive")
        
        enhancer = ImageEnhance.Contrast(image)
        enhanced = enhancer.enhance(factor)
        logger.debug(f"Applied contrast enhancement with factor {factor}")
        return enhanced
    
    def adjust_brightness(self, image: Image.Image, factor: float = 1.1) -> Image.Image:
        """
        Adjust image brightness for optimal recognition.
        
        Args:
            image: Input PIL Image
            factor: Brightness adjustment factor (1.0 = no change)
            
        Returns:
            Brightness-adjusted PIL Image
        """
        if factor <= 0:
            raise ValueError("Brightness factor must be positive")
        
        enhancer = ImageEnhance.Brightness(image)
        enhanced = enhancer.enhance(factor)
        logger.debug(f"Applied brightness adjustment with factor {factor}")
        return enhanced
    
    def enhance_saturation(self, image: Image.Image, factor: float = 1.15) -> Image.Image:
        """
        Enhance color saturation to improve currency color recognition.
        
        Args:
            image: Input PIL Image
            factor: Saturation enhancement factor (1.0 = no change)
            
        Returns:
            Saturation-enhanced PIL Image
        """
        if factor < 0:
            raise ValueError("Saturation factor must be non-negative")
        
        enhancer = ImageEnhance.Color(image)
        enhanced = enhancer.enhance(factor)
        logger.debug(f"Applied saturation enhancement with factor {factor}")
        return enhanced
    
    def sharpen_image(self, image: Image.Image, factor: float = 1.1) -> Image.Image:
        """
        Sharpen image for better text and detail recognition.
        
        Args:
            image: Input PIL Image
            factor: Sharpness factor (1.0 = no change)
            
        Returns:
            Sharpened PIL Image
        """
        if factor < 0:
            raise ValueError("Sharpness factor must be non-negative")
        
        enhancer = ImageEnhance.Sharpness(image)
        enhanced = enhancer.enhance(factor)
        logger.debug(f"Applied sharpening with factor {factor}")
        return enhanced
    
    def normalize_illumination(self, image: Image.Image) -> Image.Image:
        """
        Normalize illumination using histogram equalization.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Illumination-normalized PIL Image
        """
        # Convert to numpy array for processing
        img_array = np.array(image)
        
        if len(img_array.shape) == 3:  # Color image
            # Convert to HSV and equalize V channel
            img_hsv = image.convert('HSV')
            h, s, v = img_hsv.split()
            v_eq = ImageOps.equalize(v)
            enhanced = Image.merge('HSV', [h, s, v_eq]).convert('RGB')
        else:  # Grayscale image
            enhanced = ImageOps.equalize(image)
        
        logger.debug("Applied illumination normalization")
        return enhanced
    
    def resize_image(self, image: Image.Image, target_size: Tuple[int, int] = None) -> Image.Image:
        """
        Resize image while maintaining aspect ratio.
        
        Args:
            image: Input PIL Image
            target_size: Target (width, height) tuple
            
        Returns:
            Resized PIL Image
        """
        if target_size is None:
            target_size = self.default_settings['target_size']
        
        # Calculate size maintaining aspect ratio
        image.thumbnail(target_size, Image.Resampling.LANCZOS)
        logger.debug(f"Resized image to {image.size}")
        return image
    
    def remove_noise(self, image: Image.Image) -> Image.Image:
        """
        Apply noise reduction filter.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Noise-reduced PIL Image
        """
        # Apply median filter for noise reduction
        enhanced = image.filter(ImageFilter.MedianFilter(size=3))
        logger.debug("Applied noise reduction")
        return enhanced
    
    def full_enhancement(self, 
                        image: Image.Image,
                        contrast: float = None,
                        brightness: float = None,
                        saturation: float = None,
                        sharpness: float = None,
                        normalize_illum: bool = True,
                        reduce_noise: bool = True,
                        resize: bool = True) -> Image.Image:
        """
        Apply full enhancement pipeline with configurable parameters.
        
        Args:
            image: Input PIL Image
            contrast: Contrast factor (uses default if None)
            brightness: Brightness factor (uses default if None)
            saturation: Saturation factor (uses default if None)
            sharpness: Sharpness factor (uses default if None)
            normalize_illum: Whether to normalize illumination
            reduce_noise: Whether to apply noise reduction
            resize: Whether to resize image
            
        Returns:
            Fully enhanced PIL Image
        """
        enhanced = image.copy()
        
        # Use default values if not specified
        contrast = contrast or self.default_settings['contrast']
        brightness = brightness or self.default_settings['brightness']
        saturation = saturation or self.default_settings['saturation']
        sharpness = sharpness or self.default_settings['sharpness']
        
        try:
            # Apply enhancements in optimal order
            if normalize_illum:
                enhanced = self.normalize_illumination(enhanced)
            
            enhanced = self.enhance_contrast(enhanced, contrast)
            enhanced = self.adjust_brightness(enhanced, brightness)
            enhanced = self.enhance_saturation(enhanced, saturation)
            enhanced = self.sharpen_image(enhanced, sharpness)
            
            if reduce_noise:
                enhanced = self.remove_noise(enhanced)
            
            if resize:
                enhanced = self.resize_image(enhanced)
            
            logger.info("Full enhancement pipeline completed successfully")
            return enhanced
            
        except Exception as e:
            logger.error(f"Error in enhancement pipeline: {e}")
            raise
    
    def minimal_enhancement(self, image: Image.Image) -> Image.Image:
        """
        Apply minimal enhancement for fast processing.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Minimally enhanced PIL Image
        """
        enhanced = self.enhance_contrast(image, 1.1)
        enhanced = self.resize_image(enhanced)
        logger.info("Minimal enhancement completed")
        return enhanced
    
    def web_optimized_enhancement(self, image: Image.Image) -> Image.Image:
        """
        Enhancement optimized for web images (typically lower quality).
        
        Args:
            image: Input PIL Image
            
        Returns:
            Web-optimized enhanced PIL Image
        """
        enhanced = self.normalize_illumination(image)
        enhanced = self.enhance_contrast(enhanced, 1.3)
        enhanced = self.adjust_brightness(enhanced, 1.2)
        enhanced = self.enhance_saturation(enhanced, 1.2)
        enhanced = self.remove_noise(enhanced)
        enhanced = self.resize_image(enhanced)
        logger.info("Web-optimized enhancement completed")
        return enhanced
    
    def save_image(self, image: Image.Image, output_path: str, quality: int = None) -> None:
        """
        Save processed image to file.
        
        Args:
            image: PIL Image to save
            output_path: Output file path
            quality: JPEG quality (1-100)
        """
        quality = quality or self.default_settings['quality']
        
        if output_path.lower().endswith(('.jpg', '.jpeg')):
            image.save(output_path, 'JPEG', quality=quality, optimize=True)
        else:
            image.save(output_path)
        
        logger.info(f"Image saved to {output_path}")
    
    def get_image_info(self, image: Image.Image) -> dict:
        """
        Get image metadata and quality information.
        
        Args:
            image: PIL Image
            
        Returns:
            Dictionary with image information
        """
        return {
            'size': image.size,
            'mode': image.mode,
            'format': image.format,
            'has_transparency': image.mode in ('RGBA', 'LA') or 'transparency' in image.info,
            'estimated_quality': self._estimate_quality(image)
        }
    
    def _estimate_quality(self, image: Image.Image) -> str:
        """Estimate image quality based on size and characteristics."""
        width, height = image.size
        total_pixels = width * height
        
        if total_pixels > 2000000:  # > 2MP
            return "high"
        elif total_pixels > 500000:  # > 0.5MP
            return "medium"
        else:
            return "low"


# Convenience functions for direct use
def process_image_for_recognition(source: Union[str, bytes], 
                                enhancement_level: str = "full") -> Image.Image:
    """
    Convenience function to process image with predefined enhancement levels.
    
    Args:
        source: Image source (file path, URL, or bytes)
        enhancement_level: "minimal", "full", or "web"
        
    Returns:
        Processed PIL Image
    """
    processor = ImageProcessor()
    image = processor.load_image(source)
    
    if enhancement_level == "minimal":
        return processor.minimal_enhancement(image)
    elif enhancement_level == "web":
        return processor.web_optimized_enhancement(image)
    elif enhancement_level == "full":
        return processor.full_enhancement(image)
    else:
        raise ValueError(f"Unknown enhancement level: {enhancement_level}")