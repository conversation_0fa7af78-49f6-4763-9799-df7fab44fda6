"""
Gemini API client for Hong Kong currency recognition.
Provides configurable API access with different preset configurations.
"""

import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
from PIL import Image
import json
import os
import time
import logging
from typing import Dict, Any, Optional, Union, Generator
from dataclasses import dataclass, asdict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class GeminiConfig:
    """Configuration class for Gemini API settings."""
    temperature: float = 0.1
    top_p: float = 0.8
    top_k: int = 10
    max_output_tokens: int = 1000
    candidate_count: int = 1
    
    # Safety settings
    safety_settings: Dict[HarmCategory, HarmBlockThreshold] = None
    
    # Response format
    response_mime_type: str = "application/json"
    
    # Additional settings
    enable_streaming: bool = False
    enable_web_search: bool = False
    
    def __post_init__(self):
        """Set default safety settings if not provided."""
        if self.safety_settings is None:
            self.safety_settings = {
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }


class GeminiClient:
    """Enhanced Gemini API client for currency recognition."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize Gemini client.
        
        Args:
            api_key: Gemini API key (will use GEMINI_API_KEY env var if not provided)
        """
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable must be set or api_key provided")
        
        genai.configure(api_key=self.api_key)
        
        # Load system prompt
        self.system_prompt = self._load_system_prompt()
        
        # Predefined configurations
        self.presets = self._create_presets()
        
        # Current model
        self.model = None
        self.current_config = None
        
        logger.info("Gemini client initialized successfully")
    
    def _load_system_prompt(self) -> str:
        """Load the system prompt from prompt.md file."""
        try:
            prompt_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'prompt.md')
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            logger.warning("prompt.md not found, using basic prompt")
            return "You are a Hong Kong currency recognition system. Analyze the image and identify all banknotes and coins."
    
    def _create_presets(self) -> Dict[str, GeminiConfig]:
        """Create predefined configuration presets."""
        return {
            'high_accuracy': GeminiConfig(
                temperature=0.1,
                top_p=0.8,
                top_k=10,
                max_output_tokens=1000,
                enable_streaming=False,
                enable_web_search=False
            ),
            'balanced': GeminiConfig(
                temperature=0.3,
                top_p=0.9,
                top_k=20,
                max_output_tokens=800,
                enable_streaming=True,
                enable_web_search=False
            ),
            'fast_response': GeminiConfig(
                temperature=0.5,
                top_p=0.95,
                top_k=40,
                max_output_tokens=500,
                enable_streaming=True,
                enable_web_search=False
            ),
            'web_enhanced': GeminiConfig(
                temperature=0.2,
                top_p=0.85,
                top_k=15,
                max_output_tokens=1000,
                enable_streaming=False,
                enable_web_search=True
            ),
            'conservative': GeminiConfig(
                temperature=0.05,
                top_p=0.7,
                top_k=5,
                max_output_tokens=1200,
                enable_streaming=False,
                enable_web_search=False,
                safety_settings={
                    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
                    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
                    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
                    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
                }
            )
        }
    
    def configure_model(self, preset: str = 'high_accuracy', custom_config: Optional[GeminiConfig] = None):
        """
        Configure the model with a preset or custom configuration.
        
        Args:
            preset: Name of preset configuration
            custom_config: Custom GeminiConfig object (overrides preset)
        """
        if custom_config:
            self.current_config = custom_config
        elif preset in self.presets:
            self.current_config = self.presets[preset]
        else:
            raise ValueError(f"Unknown preset: {preset}. Available: {list(self.presets.keys())}")
        
        # Configure generation parameters
        generation_config = genai.GenerationConfig(
            temperature=self.current_config.temperature,
            top_p=self.current_config.top_p,
            top_k=self.current_config.top_k,
            max_output_tokens=self.current_config.max_output_tokens,
            candidate_count=self.current_config.candidate_count,
            response_mime_type=self.current_config.response_mime_type
        )
        
        # Initialize model
        model_name = "gemini-2.0-flash-exp" if self.current_config.enable_web_search else "gemini-2.0-flash-exp"
        
        self.model = genai.GenerativeModel(
            model_name=model_name,
            generation_config=generation_config,
            safety_settings=self.current_config.safety_settings,
            system_instruction=self.system_prompt
        )
        
        logger.info(f"Model configured with preset: {preset}")
        logger.debug(f"Configuration: {asdict(self.current_config)}")
    
    def analyze_image(self, image: Image.Image, 
                     additional_prompt: str = "",
                     timeout: int = 30) -> Dict[str, Any]:
        """
        Analyze currency image using Gemini API.
        
        Args:
            image: PIL Image to analyze
            additional_prompt: Additional context for the analysis
            timeout: Request timeout in seconds
            
        Returns:
            Dictionary with analysis results
        """
        if not self.model:
            self.configure_model()  # Use default configuration
        
        try:
            # Prepare the prompt
            prompt = additional_prompt if additional_prompt else "請分析此圖像中的香港貨幣。"
            
            start_time = time.time()
            
            if self.current_config.enable_streaming:
                response = self._analyze_with_streaming(image, prompt, timeout)
            else:
                response = self._analyze_without_streaming(image, prompt, timeout)
            
            processing_time = time.time() - start_time
            
            # Parse JSON response
            try:
                result = json.loads(response)
                result['processing_time'] = processing_time
                result['model_config'] = asdict(self.current_config)
                return result
            except json.JSONDecodeError:
                logger.error(f"Failed to parse JSON response: {response}")
                return {
                    "error": "Failed to parse API response",
                    "raw_response": response,
                    "processing_time": processing_time
                }
                
        except Exception as e:
            logger.error(f"Error analyzing image: {e}")
            return {
                "error": str(e),
                "processing_time": time.time() - start_time if 'start_time' in locals() else 0
            }
    
    def _analyze_without_streaming(self, image: Image.Image, prompt: str, timeout: int) -> str:
        """Analyze image without streaming."""
        response = self.model.generate_content(
            [prompt, image],
            request_options={"timeout": timeout}
        )
        return response.text
    
    def _analyze_with_streaming(self, image: Image.Image, prompt: str, timeout: int) -> str:
        """Analyze image with streaming response."""
        full_response = ""
        response_stream = self.model.generate_content(
            [prompt, image],
            stream=True,
            request_options={"timeout": timeout}
        )
        
        for chunk in response_stream:
            if chunk.text:
                full_response += chunk.text
        
        return full_response
    
    def batch_analyze(self, images: list[Image.Image], 
                     prompts: list[str] = None,
                     delay_between_requests: float = 1.0) -> list[Dict[str, Any]]:
        """
        Analyze multiple images in batch.
        
        Args:
            images: List of PIL Images
            prompts: List of prompts (one per image, optional)
            delay_between_requests: Delay in seconds between API calls
            
        Returns:
            List of analysis results
        """
        if prompts and len(prompts) != len(images):
            raise ValueError("Number of prompts must match number of images")
        
        results = []
        for i, image in enumerate(images):
            prompt = prompts[i] if prompts else ""
            
            logger.info(f"Analyzing image {i+1}/{len(images)}")
            result = self.analyze_image(image, prompt)
            results.append(result)
            
            # Add delay to respect API limits
            if i < len(images) - 1:
                time.sleep(delay_between_requests)
        
        return results
    
    def test_configuration(self, test_image: Image.Image) -> Dict[str, Any]:
        """
        Test current configuration with a sample image.
        
        Args:
            test_image: PIL Image for testing
            
        Returns:
            Test results including performance metrics
        """
        if not self.model:
            self.configure_model()
        
        test_prompt = "This is a test. Please respond with a simple JSON object containing 'status': 'test_successful'."
        
        start_time = time.time()
        result = self.analyze_image(test_image, test_prompt)
        end_time = time.time()
        
        return {
            "config_name": "current",
            "response_time": end_time - start_time,
            "success": "error" not in result,
            "result": result,
            "configuration": asdict(self.current_config)
        }
    
    def benchmark_presets(self, test_image: Image.Image) -> Dict[str, Dict[str, Any]]:
        """
        Benchmark all preset configurations.
        
        Args:
            test_image: PIL Image for testing
            
        Returns:
            Dictionary with benchmark results for each preset
        """
        benchmark_results = {}
        original_config = self.current_config
        
        for preset_name in self.presets.keys():
            logger.info(f"Benchmarking preset: {preset_name}")
            
            try:
                self.configure_model(preset_name)
                result = self.test_configuration(test_image)
                benchmark_results[preset_name] = result
                
                # Small delay between tests
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error benchmarking {preset_name}: {e}")
                benchmark_results[preset_name] = {
                    "error": str(e),
                    "success": False
                }
        
        # Restore original configuration
        if original_config:
            self.current_config = original_config
            self._reconfigure_model()
        
        return benchmark_results
    
    def _reconfigure_model(self):
        """Reconfigure model with current configuration."""
        if self.current_config:
            generation_config = genai.GenerationConfig(
                temperature=self.current_config.temperature,
                top_p=self.current_config.top_p,
                top_k=self.current_config.top_k,
                max_output_tokens=self.current_config.max_output_tokens,
                candidate_count=self.current_config.candidate_count,
                response_mime_type=self.current_config.response_mime_type
            )
            
            model_name = "gemini-2.0-flash-exp"
            
            self.model = genai.GenerativeModel(
                model_name=model_name,
                generation_config=generation_config,
                safety_settings=self.current_config.safety_settings,
                system_instruction=self.system_prompt
            )
    
    def get_available_presets(self) -> list[str]:
        """Get list of available preset names."""
        return list(self.presets.keys())
    
    def get_current_config(self) -> Optional[Dict[str, Any]]:
        """Get current configuration as dictionary."""
        return asdict(self.current_config) if self.current_config else None


# Convenience functions
def create_gemini_client(api_key: Optional[str] = None, preset: str = 'high_accuracy') -> GeminiClient:
    """
    Create and configure a Gemini client with specified preset.
    
    Args:
        api_key: Gemini API key
        preset: Configuration preset name
        
    Returns:
        Configured GeminiClient instance
    """
    client = GeminiClient(api_key)
    client.configure_model(preset)
    return client