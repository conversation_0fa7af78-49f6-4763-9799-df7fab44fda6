"""
Main currency recognition module that integrates image processing and Gemini API.
"""

from PIL import Image
import json
import os
import time
import logging
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass

from .image_processor import ImageProcessor, process_image_for_recognition
from .gemini_client import GeminiClient, GeminiConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class RecognitionResult:
    """Result container for currency recognition."""
    success: bool
    analysis: Dict[str, Any]
    processing_time: float
    config_used: str
    preprocessing_applied: bool
    image_info: Dict[str, Any]
    error_message: Optional[str] = None


class CurrencyRecognizer:
    """Hong Kong Currency Recognition System."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the currency recognizer.
        
        Args:
            api_key: Gemini API key (uses GEMINI_API_KEY env var if not provided)
        """
        self.image_processor = ImageProcessor()
        self.gemini_client = GeminiClient(api_key)
        
        # Load preset configurations
        self.config_presets = self._load_config_presets()
        
        logger.info("Currency recognizer initialized successfully")
    
    def _load_config_presets(self) -> Dict[str, Any]:
        """Load configuration presets from JSON file."""
        try:
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)), 
                'config', 
                'api_presets.json'
            )
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("api_presets.json not found, using default configurations")
            return {"presets": {}, "test_configurations": {}}
    
    def recognize_currency(self, 
                          image_source: Union[str, bytes, Image.Image],
                          preset_name: str = "high_accuracy_local",
                          enable_preprocessing: bool = True,
                          custom_prompt: str = "",
                          save_processed_image: bool = False,
                          output_dir: str = "output") -> RecognitionResult:
        """
        Recognize Hong Kong currency from image.
        
        Args:
            image_source: Image source (file path, URL, bytes, or PIL Image)
            preset_name: Configuration preset to use
            enable_preprocessing: Whether to apply image preprocessing
            custom_prompt: Additional prompt for the API
            save_processed_image: Whether to save the processed image
            output_dir: Directory to save processed images
            
        Returns:
            RecognitionResult object with analysis results
        """
        start_time = time.time()
        
        try:
            # Load image
            if isinstance(image_source, Image.Image):
                original_image = image_source
            else:
                original_image = self.image_processor.load_image(image_source)
            
            # Get image info
            image_info = self.image_processor.get_image_info(original_image)
            
            # Apply preprocessing if enabled
            if enable_preprocessing and preset_name in self.config_presets.get("presets", {}):
                preset_config = self.config_presets["presets"][preset_name]
                preprocessing_config = preset_config.get("image_preprocessing", {})
                
                processed_image = self._apply_preprocessing(original_image, preprocessing_config)
            else:
                processed_image = original_image
            
            # Save processed image if requested
            if save_processed_image:
                self._save_processed_image(processed_image, output_dir, preset_name)
            
            # Configure Gemini client
            self.gemini_client.configure_model(preset_name)
            
            # Analyze image
            analysis_result = self.gemini_client.analyze_image(
                processed_image, 
                custom_prompt
            )
            
            processing_time = time.time() - start_time
            
            return RecognitionResult(
                success="error" not in analysis_result,
                analysis=analysis_result,
                processing_time=processing_time,
                config_used=preset_name,
                preprocessing_applied=enable_preprocessing,
                image_info=image_info,
                error_message=analysis_result.get("error")
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error in currency recognition: {e}")
            
            return RecognitionResult(
                success=False,
                analysis={},
                processing_time=processing_time,
                config_used=preset_name,
                preprocessing_applied=enable_preprocessing,
                image_info={},
                error_message=str(e)
            )
    
    def _apply_preprocessing(self, image: Image.Image, config: Dict[str, Any]) -> Image.Image:
        """Apply preprocessing based on configuration."""
        enhancement_level = config.get("enhancement_level", "full")
        
        if enhancement_level == "minimal":
            return self.image_processor.minimal_enhancement(image)
        elif enhancement_level == "web":
            return self.image_processor.web_optimized_enhancement(image)
        elif enhancement_level == "full":
            return self.image_processor.full_enhancement(
                image,
                contrast=config.get("contrast", 1.2),
                brightness=config.get("brightness", 1.1),
                saturation=config.get("saturation", 1.15),
                sharpness=config.get("sharpness", 1.1),
                normalize_illum=config.get("normalize_illumination", True),
                reduce_noise=config.get("reduce_noise", True)
            )
        else:
            logger.warning(f"Unknown enhancement level: {enhancement_level}, using original image")
            return image
    
    def _save_processed_image(self, image: Image.Image, output_dir: str, preset_name: str):
        """Save processed image to output directory."""
        try:
            os.makedirs(output_dir, exist_ok=True)
            timestamp = int(time.time())
            filename = f"processed_{preset_name}_{timestamp}.jpg"
            output_path = os.path.join(output_dir, filename)
            self.image_processor.save_image(image, output_path)
            logger.info(f"Processed image saved to {output_path}")
        except Exception as e:
            logger.error(f"Error saving processed image: {e}")
    
    def batch_recognize(self, 
                       image_sources: List[Union[str, bytes, Image.Image]],
                       preset_name: str = "high_accuracy_local",
                       enable_preprocessing: bool = True) -> List[RecognitionResult]:
        """
        Recognize currency from multiple images.
        
        Args:
            image_sources: List of image sources
            preset_name: Configuration preset to use
            enable_preprocessing: Whether to apply image preprocessing
            
        Returns:
            List of RecognitionResult objects
        """
        results = []
        
        for i, image_source in enumerate(image_sources):
            logger.info(f"Processing image {i+1}/{len(image_sources)}")
            
            result = self.recognize_currency(
                image_source,
                preset_name,
                enable_preprocessing
            )
            results.append(result)
            
            # Small delay between API calls
            if i < len(image_sources) - 1:
                time.sleep(1)
        
        return results
    
    def test_all_presets(self, test_image_source: Union[str, bytes, Image.Image]) -> Dict[str, RecognitionResult]:
        """
        Test all available presets with a single image.
        
        Args:
            test_image_source: Image source for testing
            
        Returns:
            Dictionary with results for each preset
        """
        results = {}
        available_presets = list(self.config_presets.get("presets", {}).keys())
        
        if not available_presets:
            available_presets = self.gemini_client.get_available_presets()
        
        for preset_name in available_presets:
            logger.info(f"Testing preset: {preset_name}")
            
            try:
                result = self.recognize_currency(
                    test_image_source,
                    preset_name,
                    enable_preprocessing=True
                )
                results[preset_name] = result
                
                # Small delay between tests
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error testing preset {preset_name}: {e}")
                results[preset_name] = RecognitionResult(
                    success=False,
                    analysis={},
                    processing_time=0,
                    config_used=preset_name,
                    preprocessing_applied=True,
                    image_info={},
                    error_message=str(e)
                )
        
        return results
    
    def compare_preprocessing_effects(self, 
                                    image_source: Union[str, bytes, Image.Image],
                                    preset_name: str = "high_accuracy_local") -> Dict[str, RecognitionResult]:
        """
        Compare recognition results with and without preprocessing.
        
        Args:
            image_source: Image source for comparison
            preset_name: Configuration preset to use
            
        Returns:
            Dictionary with results for preprocessed and original images
        """
        results = {}
        
        # Test without preprocessing
        logger.info("Testing without preprocessing")
        results["no_preprocessing"] = self.recognize_currency(
            image_source,
            preset_name,
            enable_preprocessing=False
        )
        
        # Test with preprocessing
        logger.info("Testing with preprocessing")
        results["with_preprocessing"] = self.recognize_currency(
            image_source,
            preset_name,
            enable_preprocessing=True
        )
        
        return results
    
    def get_available_presets(self) -> List[str]:
        """Get list of available configuration presets."""
        config_presets = list(self.config_presets.get("presets", {}).keys())
        gemini_presets = self.gemini_client.get_available_presets()
        
        return list(set(config_presets + gemini_presets))
    
    def validate_currency_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and enhance currency recognition result.
        
        Args:
            result: Raw recognition result from API
            
        Returns:
            Validated and enhanced result
        """
        validation_result = {
            "valid_structure": False,
            "total_banknotes": 0,
            "total_coins": 0,
            "total_value": 0.0,
            "confidence_scores": [],
            "warnings": []
        }
        
        try:
            # Check basic structure
            if "紙幣" in result and "硬幣" in result:
                validation_result["valid_structure"] = True
                
                # Count banknotes and calculate values
                banknotes = result.get("紙幣", [])
                if isinstance(banknotes, list):
                    validation_result["total_banknotes"] = sum(item.get("數量", 0) for item in banknotes)
                    for item in banknotes:
                        try:
                            denomination = item.get("面額", "0元").replace("元", "")
                            value = float(denomination) * item.get("數量", 0)
                            validation_result["total_value"] += value
                            
                            confidence = item.get("置信度", 0)
                            validation_result["confidence_scores"].append(confidence)
                            
                            if confidence < 0.7:
                                validation_result["warnings"].append(
                                    f"Low confidence ({confidence}) for {item.get('面額', 'unknown')} banknote"
                                )
                        except (ValueError, TypeError):
                            validation_result["warnings"].append(f"Invalid denomination format: {item}")
                
                # Count coins
                coins = result.get("硬幣", [])
                if isinstance(coins, list):
                    validation_result["total_coins"] = sum(item.get("數量", 0) for item in coins)
                    for item in coins:
                        try:
                            denomination = item.get("面額", "0元").replace("元", "")
                            value = float(denomination) * item.get("數量", 0)
                            validation_result["total_value"] += value
                            
                            confidence = item.get("置信度", 0)
                            validation_result["confidence_scores"].append(confidence)
                            
                            if confidence < 0.7:
                                validation_result["warnings"].append(
                                    f"Low confidence ({confidence}) for {item.get('面額', 'unknown')} coin"
                                )
                        except (ValueError, TypeError):
                            validation_result["warnings"].append(f"Invalid denomination format: {item}")
            
            # Calculate average confidence
            if validation_result["confidence_scores"]:
                validation_result["average_confidence"] = sum(validation_result["confidence_scores"]) / len(validation_result["confidence_scores"])
            else:
                validation_result["average_confidence"] = 0.0
                
        except Exception as e:
            validation_result["warnings"].append(f"Validation error: {str(e)}")
        
        return validation_result


def create_currency_recognizer(api_key: Optional[str] = None) -> CurrencyRecognizer:
    """
    Create a currency recognizer instance.
    
    Args:
        api_key: Gemini API key
        
    Returns:
        CurrencyRecognizer instance
    """
    return CurrencyRecognizer(api_key)